2025-07-19 00:29:37:324 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 23.0.1 with PID 29085 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-07-19 00:29:37:325 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-07-19 00:29:38:498 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-07-19 00:29:38:498 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-07-19 00:29:38:529 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-07-19 00:29:39:627 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-07-19 00:29:39:636 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-07-19 00:29:39:636 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-19 00:29:40:308 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-19 00:29:41:459 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-07-19 00:29:41:483 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-07-19 00:29:41:598 INFO  [redisson-netty-5-8] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-07-19 00:29:42:357 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-07-19 00:29:42:388 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-07-19 00:29:42:389 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-07-19 00:29:43:893 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 6.877 seconds (process running for 7.119)
2025-07-19 00:35:06:358 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 00:38:34:465 INFO  [http-nio-7001-exec-6] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=aed3cb9b-7af0-42b3-b292-650d18b2e0fc, createTime=1752856714462, level=0, moduleId=01, method=org.opsli.modulars.system.user.web.UserRestController.update, args=[{"realName":"admin","mobile":"13700000000","email":"<EMAIL>","no":"cyj001","tenantId":"1944596258485932033","izTenantAdmin":"1","enableSwitchTenant":"0","expireTime":1753891200000,"id":"1944596700989198337","createBy":"1944590928460877826","createTime":1752462957000,"version":1,"izApi":false,"izManual":false}], userId=1, username=system, realName=超级管理员, description='超级管理员'=> 修改用户信息, operationType=update, runTime=352, returnValue={"msg":"修改用户信息成功","code":0,"timestamp":1752856714460}, tenantId=0, logType=2)
2025-07-19 00:45:04:774 INFO  [http-nio-7001-exec-8] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=abe8210d-0fd9-4e4f-9d99-1dfe33ad8516, createTime=1752857104774, level=0, moduleId=03, method=org.opsli.modulars.system.menu.web.MenuRestController.update, args=[{"parentId":"1944613334869053441","menuName":"部门管理","icon":"","type":"1","url":"/system/deptManagement","component":"views/modules/system/deptManagement/index","sortNo":2,"hidden":"0","alwaysShow":"0","label":"1,0","id":"1944613700432007170","createBy":"1","createTime":1752467010000,"version":2,"izApi":false,"izManual":false}], userId=1, username=system, realName=超级管理员, description='超级管理员'=> 修改菜单, operationType=update, runTime=558, returnValue={"msg":"修改菜单成功","code":0,"timestamp":1752857104773}, tenantId=0, logType=2)
2025-07-19 00:49:02:347 INFO  [http-nio-7001-exec-4] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=7278cbdf-e605-4ef0-a9a1-9c83a7f522b3, createTime=1752857342346, level=0, moduleId=03, method=org.opsli.modulars.system.menu.web.MenuRestController.update, args=[{"parentId":"1944613334869053441","menuName":"部门管理","icon":"","type":"1","url":"department","component":"views/modules/system/deptManagement/index","sortNo":2,"hidden":"0","alwaysShow":"0","label":"1,0","id":"1944613700432007170","createBy":"1","createTime":1752467010000,"version":3,"izApi":false,"izManual":false}], userId=1, username=system, realName=超级管理员, description='超级管理员'=> 修改菜单, operationType=update, runTime=494, returnValue={"msg":"修改菜单成功","code":0,"timestamp":1752857342345}, tenantId=0, logType=2)
2025-07-19 00:57:03:024 INFO  [http-nio-7001-exec-3] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=aa3bef66-09fe-496b-8983-634d1041e5eb, createTime=1752857823023, level=0, moduleId=03, method=org.opsli.modulars.system.menu.web.MenuRestController.update, args=[{"parentId":"1944613334869053441","menuName":"部门管理","icon":"","type":"1","url":"dept","component":"views/modules/system/deptManagement/index","sortNo":2,"hidden":"0","alwaysShow":"0","label":"1,0","id":"1944613700432007170","createBy":"1","createTime":1752467010000,"version":4,"izApi":false,"izManual":false}], userId=1, username=system, realName=超级管理员, description='超级管理员'=> 修改菜单, operationType=update, runTime=509, returnValue={"msg":"修改菜单成功","code":0,"timestamp":1752857823023}, tenantId=0, logType=2)
2025-07-19 01:01:10:492 INFO  [http-nio-7001-exec-1] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=e497773f-f5bc-40a7-96e3-7ab6974ce455, createTime=1752858070492, level=0, moduleId=03, method=org.opsli.modulars.system.menu.web.MenuRestController.update, args=[{"parentId":"1944613334869053441","menuName":"部门管理","icon":"","type":"1","url":"department","component":"views/modules/system/deptManagement/index","sortNo":2,"hidden":"0","alwaysShow":"0","label":"1,0","id":"1944613700432007170","createBy":"1","createTime":1752467010000,"version":5,"izApi":false,"izManual":false}], userId=1, username=system, realName=超级管理员, description='超级管理员'=> 修改菜单, operationType=update, runTime=432, returnValue={"msg":"修改菜单成功","code":0,"timestamp":1752858070490}, tenantId=0, logType=2)
2025-07-19 01:08:24:014 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 23.0.1 with PID 32365 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-07-19 01:08:24:014 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-07-19 01:08:25:367 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-07-19 01:08:25:368 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-07-19 01:08:25:406 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-07-19 01:08:28:623 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-07-19 01:08:28:624 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-07-19 01:08:28:625 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-19 01:08:29:281 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-19 01:08:30:472 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-07-19 01:08:30:503 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-07-19 01:08:30:633 INFO  [redisson-netty-5-8] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-07-19 01:08:31:401 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-07-19 01:08:31:445 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-07-19 01:08:31:446 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-07-19 01:08:33:007 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 9.307 seconds (process running for 9.616)
2025-07-19 01:08:44:509 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 01:12:01:059 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 23.0.1 with PID 33023 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-07-19 01:12:01:059 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-07-19 01:12:02:284 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-07-19 01:12:02:285 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-07-19 01:12:02:315 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-07-19 01:12:03:490 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-07-19 01:12:03:499 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-07-19 01:12:03:499 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-19 01:12:04:031 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-19 01:12:05:195 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-07-19 01:12:05:219 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-07-19 01:12:05:336 INFO  [redisson-netty-5-8] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-07-19 01:12:06:066 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-07-19 01:12:06:092 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-07-19 01:12:06:093 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-07-19 01:12:07:788 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 7.001 seconds (process running for 7.225)
2025-07-19 01:12:28:825 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 01:32:39:880 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 23.0.1 with PID 34850 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-07-19 01:32:39:880 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-07-19 01:32:41:305 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-07-19 01:32:41:306 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-07-19 01:32:41:337 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-07-19 01:32:44:567 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-07-19 01:32:44:568 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-07-19 01:32:44:568 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-19 01:32:45:111 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-19 01:32:46:337 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-07-19 01:32:46:369 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-07-19 01:32:46:473 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-07-19 01:32:47:245 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-07-19 01:32:47:271 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-07-19 01:32:47:273 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-07-19 01:32:49:699 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 10.115 seconds (process running for 10.426)
2025-07-19 01:33:06:905 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 01:33:23:207 INFO  [http-nio-7001-exec-8] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=1ca27e5d-cff5-4e1f-b13e-bedbea3270b8, createTime=1752860003204, level=0, moduleId=00, method=org.opsli.modulars.system.department.web.DepartmentRestController.insert, args=[{"tenantId":1,"dataMonth":1751299200000,"name":"人力部","deptCode":"hr","status":1,"responsibilities":"hello","version":0,"izApi":false,"izManual":false}], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 新增部门数据, operationType=insert, runTime=217, returnValue={"msg":"新增部门数据成功","code":0,"timestamp":1752860003201}, tenantId=1944596258485932033, logType=2)
2025-07-19 02:18:05:067 INFO  [http-nio-7001-exec-6] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=ce1854e5-cdf2-4b8f-a23a-06dbd18f74ed, createTime=1752862685066, level=0, moduleId=00, method=org.opsli.modulars.system.department.web.DepartmentRestController.insert, args=[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"name":"人事部","deptCode":"rsb","status":1,"responsibilities":"world","version":0,"izApi":false,"izManual":false}], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 新增部门数据, operationType=insert, runTime=284, returnValue={"msg":"新增部门数据成功","code":0,"timestamp":1752862685066}, tenantId=1944596258485932033, logType=2)
2025-07-19 02:30:27:744 INFO  [http-nio-7001-exec-5] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=c451e5f1-9ac8-4623-92af-fdc48ac378e0, createTime=1752863427744, level=0, moduleId=00, method=org.opsli.modulars.system.department.web.DepartmentRestController.insert, args=[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"name":"人是一部","deptCode":"rsyb","parentDepartmentId":1946273281545535489,"status":1,"responsibilities":"","version":0,"izApi":false,"izManual":false}], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 新增部门数据, operationType=insert, runTime=274, returnValue={"msg":"新增部门数据成功","code":0,"timestamp":1752863427743}, tenantId=1944596258485932033, logType=2)
2025-07-19 02:32:24:383 INFO  [http-nio-7001-exec-9] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=64ef3d1f-e625-4f2f-8d5e-dd565b5335a5, createTime=1752863544383, level=0, moduleId=00, method=org.opsli.modulars.system.department.web.DepartmentRestController.del, args=["1946276396550258690"], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 删除部门数据, operationType=delete, runTime=174, returnValue={"msg":"删除部门数据成功","code":0,"timestamp":1752863544382}, tenantId=1944596258485932033, logType=2)
2025-07-19 02:32:28:266 INFO  [http-nio-7001-exec-9] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=6e0cce29-5071-4cd5-8e38-9be3d896be9a, createTime=1752863548265, level=0, moduleId=00, method=org.opsli.modulars.system.department.web.DepartmentRestController.del, args=["1946273281545535489"], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 删除部门数据, operationType=delete, runTime=129, returnValue={"msg":"删除部门数据成功","code":0,"timestamp":1752863548265}, tenantId=1944596258485932033, logType=2)
2025-07-19 10:14:17:829 INFO  [http-nio-7001-exec-8] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=41251527-8570-4d4d-9da4-bc5af96323f9, createTime=1752891257829, level=0, moduleId=00, method=org.opsli.modulars.system.department.web.DepartmentRestController.insert, args=[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"name":"人事部","deptCode":"rsb","status":1,"responsibilities":"","version":0,"izApi":false,"izManual":false}], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 新增部门数据, operationType=insert, runTime=180, returnValue={"msg":"新增部门数据成功","code":0,"timestamp":1752891257828}, tenantId=1944596258485932033, logType=2)
2025-07-19 10:14:36:588 INFO  [http-nio-7001-exec-2] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=ae461124-1ce5-4212-ac84-0061bc100d35, createTime=1752891276588, level=0, moduleId=00, method=org.opsli.modulars.system.department.web.DepartmentRestController.insert, args=[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"name":"研发部","deptCode":"yfb","status":1,"responsibilities":"","version":0,"izApi":false,"izManual":false}], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 新增部门数据, operationType=insert, runTime=175, returnValue={"msg":"新增部门数据成功","code":0,"timestamp":1752891276588}, tenantId=1944596258485932033, logType=2)
2025-07-19 10:23:54:148 INFO  [http-nio-7001-exec-4] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=9da6724d-cbaa-4924-a5b6-de733c75ad22, createTime=1752891834148, level=0, moduleId=00, method=org.opsli.modulars.system.department.web.DepartmentRestController.insert, args=[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"name":"总经办","deptCode":"zjb","status":1,"responsibilities":"","version":0,"izApi":false,"izManual":false}], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 新增部门数据, operationType=insert, runTime=255, returnValue={"msg":"新增部门数据成功","code":0,"timestamp":1752891834147}, tenantId=1944596258485932033, logType=2)
2025-07-19 10:24:08:290 INFO  [http-nio-7001-exec-8] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=26155c35-106f-4f9d-b951-b533b342152c, createTime=1752891848289, level=0, moduleId=00, method=org.opsli.modulars.system.department.web.DepartmentRestController.insert, args=[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"name":"产品部","deptCode":"cpb","status":1,"responsibilities":"","version":0,"izApi":false,"izManual":false}], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 新增部门数据, operationType=insert, runTime=180, returnValue={"msg":"新增部门数据成功","code":0,"timestamp":1752891848289}, tenantId=1944596258485932033, logType=2)
2025-07-19 10:24:21:265 INFO  [http-nio-7001-exec-4] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=7b7955e8-7094-450a-8516-3ab53d944e43, createTime=1752891861264, level=0, moduleId=00, method=org.opsli.modulars.system.department.web.DepartmentRestController.insert, args=[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"name":"设计部","deptCode":"sjb","status":1,"responsibilities":"","version":0,"izApi":false,"izManual":false}], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 新增部门数据, operationType=insert, runTime=184, returnValue={"msg":"新增部门数据成功","code":0,"timestamp":1752891861264}, tenantId=1944596258485932033, logType=2)
2025-07-19 10:24:37:109 INFO  [http-nio-7001-exec-2] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=cb1031bc-0103-4c3d-b601-b42bbfc23cd3, createTime=1752891877108, level=0, moduleId=00, method=org.opsli.modulars.system.department.web.DepartmentRestController.insert, args=[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"name":"运营部","deptCode":"yyb","status":1,"responsibilities":"","version":0,"izApi":false,"izManual":false}], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 新增部门数据, operationType=insert, runTime=183, returnValue={"msg":"新增部门数据成功","code":0,"timestamp":1752891877108}, tenantId=1944596258485932033, logType=2)
2025-07-19 10:24:56:621 INFO  [http-nio-7001-exec-2] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=144f3f8d-d851-49c8-9423-e5f8878930ba, createTime=1752891896620, level=0, moduleId=00, method=org.opsli.modulars.system.department.web.DepartmentRestController.insert, args=[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"name":"客服部","deptCode":"kfb","status":1,"responsibilities":"","version":0,"izApi":false,"izManual":false}], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 新增部门数据, operationType=insert, runTime=167, returnValue={"msg":"新增部门数据成功","code":0,"timestamp":1752891896620}, tenantId=1944596258485932033, logType=2)
2025-07-19 10:25:17:891 INFO  [http-nio-7001-exec-4] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=ba3d8b86-af9b-4ef7-9f08-c15972b52c10, createTime=1752891917891, level=0, moduleId=00, method=org.opsli.modulars.system.department.web.DepartmentRestController.insert, args=[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"name":"市场部","deptCode":"scb","status":1,"responsibilities":"","version":0,"izApi":false,"izManual":false}], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 新增部门数据, operationType=insert, runTime=245, returnValue={"msg":"新增部门数据成功","code":0,"timestamp":1752891917890}, tenantId=1944596258485932033, logType=2)
2025-07-19 10:25:37:311 INFO  [http-nio-7001-exec-4] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=afdf0bd3-498a-43d3-b02f-eec5bc2b8ae5, createTime=1752891937311, level=0, moduleId=00, method=org.opsli.modulars.system.department.web.DepartmentRestController.insert, args=[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"name":"新业务部","deptCode":"xyeb","status":1,"responsibilities":"","version":0,"izApi":false,"izManual":false}], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 新增部门数据, operationType=insert, runTime=195, returnValue={"msg":"新增部门数据成功","code":0,"timestamp":1752891937310}, tenantId=1944596258485932033, logType=2)
2025-07-19 10:26:00:052 INFO  [http-nio-7001-exec-10] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=1b7d70e9-babf-4880-8733-5c26da244667, createTime=1752891960051, level=0, moduleId=00, method=org.opsli.modulars.system.department.web.DepartmentRestController.insert, args=[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"name":"数据分析部","deptCode":"sjfx","status":1,"responsibilities":"","version":0,"izApi":false,"izManual":false}], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 新增部门数据, operationType=insert, runTime=196, returnValue={"msg":"新增部门数据成功","code":0,"timestamp":1752891960048}, tenantId=1944596258485932033, logType=2)
2025-07-19 10:26:19:524 INFO  [http-nio-7001-exec-8] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=b4eb4637-4ce4-415f-9b41-3d4380d25da6, createTime=1752891979524, level=0, moduleId=00, method=org.opsli.modulars.system.department.web.DepartmentRestController.insert, args=[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"name":"测试部","deptCode":"csb","status":1,"responsibilities":"","version":0,"izApi":false,"izManual":false}], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 新增部门数据, operationType=insert, runTime=187, returnValue={"msg":"新增部门数据成功","code":0,"timestamp":1752891979523}, tenantId=1944596258485932033, logType=2)
2025-07-19 10:58:22:000 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 23.0.1 with PID 69714 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-07-19 10:58:22:002 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-07-19 10:58:23:667 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-07-19 10:58:23:667 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-07-19 10:58:23:708 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-07-19 10:58:24:997 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-07-19 10:58:24:999 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-07-19 10:58:24:999 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-19 10:58:25:551 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-19 10:58:26:819 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-07-19 10:58:26:850 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-07-19 10:58:26:956 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-07-19 10:58:27:739 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-07-19 10:58:27:770 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-07-19 10:58:27:771 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-07-19 10:58:29:301 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 7.76 seconds (process running for 8.492)
2025-07-19 10:59:01:025 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 11:05:18:959 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 23.0.1 with PID 70619 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-07-19 11:05:18:959 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-07-19 11:05:20:175 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-07-19 11:05:20:176 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-07-19 11:05:20:261 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-07-19 11:05:23:517 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-07-19 11:05:23:519 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-07-19 11:05:23:519 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-19 11:05:24:044 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-19 11:05:25:238 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-07-19 11:05:25:270 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-07-19 11:05:25:382 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-07-19 11:05:26:152 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-07-19 11:05:26:194 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-07-19 11:05:26:195 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-07-19 11:05:27:739 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 9.052 seconds (process running for 9.3)
2025-07-19 11:06:06:327 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 11:06:06:395 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求开始 -------------
2025-07-19 11:06:06:395 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/common/public-key
2025-07-19 11:06:06:395 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:06:06:395 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: CommonRestController.getPublicKey
2025-07-19 11:06:06:397 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752894366206.3108"]
}
2025-07-19 11:06:06:456 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求结束 => 耗时: 61ms -------------
2025-07-19 11:06:06:477 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-07-19 11:06:06:478 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-07-19 11:06:06:478 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-19 11:06:06:478 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LoginByAccountRestController.login
2025-07-19 11:06:06:879 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 402ms -------------
2025-07-19 11:06:06:889 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/slipCount" 请求开始 -------------
2025-07-19 11:06:06:889 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/slipCount
2025-07-19 11:06:06:889 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:06:06:889 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LoginCommonRestController.slipCount
2025-07-19 11:06:06:889 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"username":["admin_cyj"]
}
2025-07-19 11:06:06:908 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/slipCount" 请求结束 => 耗时: 19ms -------------
2025-07-19 11:06:15:441 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-07-19 11:06:15:441 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-07-19 11:06:15:441 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-19 11:06:15:441 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LoginByAccountRestController.login
2025-07-19 11:06:16:160 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 719ms -------------
2025-07-19 11:06:16:290 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-19 11:06:16:290 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-19 11:06:16:291 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:06:16:291 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-19 11:06:16:420 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 130ms -------------
2025-07-19 11:06:16:505 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-07-19 11:06:16:505 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-07-19 11:06:16:505 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-19 11:06:16:506 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-07-19 11:06:16:554 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 49ms -------------
2025-07-19 11:06:17:031 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-19 11:06:17:031 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-19 11:06:17:031 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:06:17:031 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-19 11:06:17:150 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 119ms -------------
2025-07-19 11:06:21:457 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:06:21:457 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:06:21:457 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:06:21:457 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:06:21:457 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752894381370.0183"],
	"typeCode":["common_status"]
}
2025-07-19 11:06:21:607 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 150ms -------------
2025-07-19 11:06:21:723 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:06:21:723 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:06:21:723 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:06:21:723 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:06:21:723 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752894381635.4111"],
	"typeCode":["common_status"]
}
2025-07-19 11:06:21:861 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 138ms -------------
2025-07-19 11:06:21:977 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:06:21:977 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-19 11:06:21:977 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-19 11:06:21:977 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:06:21:977 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:06:21:977 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:06:21:977 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-19 11:06:21:977 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:06:21:977 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:06:22:098 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 121ms -------------
2025-07-19 11:06:22:200 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 223ms -------------
2025-07-19 11:06:26:889 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:06:26:889 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:06:26:890 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:06:26:890 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:06:26:890 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-06"]
}
2025-07-19 11:06:27:049 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 160ms -------------
2025-07-19 11:09:44:756 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/insert" 请求开始 -------------
2025-07-19 11:09:44:756 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/insert
2025-07-19 11:09:44:757 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-19 11:09:44:757 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.insert
2025-07-19 11:09:45:115 INFO  [http-nio-7001-exec-7] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=790cdeaa-311a-4da6-876c-b7b3dfaf3bb5, createTime=1752894585107, level=0, moduleId=00, method=org.opsli.modulars.system.department.web.DepartmentRestController.insert, args=[{"tenantId":1944596258485932033,"dataMonth":1748707200000,"name":"行政部","deptCode":"xzb","status":1,"responsibilities":"","version":0,"izApi":false,"izManual":false}], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 新增部门数据, operationType=insert, runTime=263, returnValue={"msg":"新增部门数据成功","code":0,"timestamp":1752894585105}, tenantId=1944596258485932033, logType=2)
2025-07-19 11:09:45:121 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/insert" 请求结束 => 耗时: 366ms -------------
2025-07-19 11:09:45:242 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:09:45:242 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:09:45:242 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:09:45:242 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:09:45:243 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-06"]
}
2025-07-19 11:09:45:418 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 176ms -------------
2025-07-19 11:23:12:322 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 23.0.1 with PID 72203 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-07-19 11:23:12:322 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-07-19 11:23:13:585 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-07-19 11:23:13:585 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-07-19 11:23:13:623 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-07-19 11:23:16:817 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-07-19 11:23:16:818 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-07-19 11:23:16:819 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-19 11:23:17:360 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-19 11:23:18:553 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-07-19 11:23:18:582 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-07-19 11:23:18:684 INFO  [redisson-netty-5-6] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-07-19 11:23:19:484 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-07-19 11:23:19:528 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-07-19 11:23:19:529 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-07-19 11:23:21:171 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 9.146 seconds (process running for 9.527)
2025-07-19 11:23:28:327 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 11:23:28:723 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-19 11:23:28:723 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-19 11:23:28:723 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:23:28:723 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-19 11:23:28:959 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 236ms -------------
2025-07-19 11:23:29:091 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-07-19 11:23:29:091 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-07-19 11:23:29:091 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-19 11:23:29:091 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-07-19 11:23:29:161 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 70ms -------------
2025-07-19 11:23:29:585 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:23:29:585 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:23:29:585 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:23:29:585 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:23:29:586 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752895409461.748"],
	"typeCode":["common_status"]
}
2025-07-19 11:23:29:950 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 365ms -------------
2025-07-19 11:23:30:100 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:23:30:100 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:23:30:100 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:23:30:100 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:23:30:100 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752895409981.3423"],
	"typeCode":["common_status"]
}
2025-07-19 11:23:30:276 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 176ms -------------
2025-07-19 11:23:30:433 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-19 11:23:30:433 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-19 11:23:30:433 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:23:30:433 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-19 11:23:30:443 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:23:30:443 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:23:30:444 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:23:30:444 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:23:30:444 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:23:30:606 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 173ms -------------
2025-07-19 11:23:30:788 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 345ms -------------
2025-07-19 11:23:52:377 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:23:52:378 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:23:52:378 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:23:52:379 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:23:52:379 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-06"]
}
2025-07-19 11:23:52:623 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 246ms -------------
2025-07-19 11:25:10:550 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/logs/findPage" 请求开始 -------------
2025-07-19 11:25:10:551 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/logs/findPage
2025-07-19 11:25:10:551 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:25:10:551 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LogsRestController.findPage
2025-07-19 11:25:10:552 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"type_EQ":[""],
	"createTime_BEGIN":[""],
	"createTime_END":[""]
}
2025-07-19 11:25:10:594 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/logs/findPage" 请求结束 => 耗时: 44ms -------------
2025-07-19 11:25:12:154 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:25:12:155 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:25:12:155 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:25:12:155 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:25:12:155 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752895512020.2341"],
	"typeCode":["common_status"]
}
2025-07-19 11:25:12:364 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 210ms -------------
2025-07-19 11:25:12:516 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:25:12:516 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:25:12:516 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:25:12:516 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:25:12:516 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752895512388.7327"],
	"typeCode":["common_status"]
}
2025-07-19 11:25:12:700 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 185ms -------------
2025-07-19 11:25:12:863 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:25:12:864 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:25:12:864 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:25:12:864 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:25:12:864 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-06"]
}
2025-07-19 11:25:13:055 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 192ms -------------
2025-07-19 11:25:17:568 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:25:17:568 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:25:17:568 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:25:17:569 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:25:17:569 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:25:17:764 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 196ms -------------
2025-07-19 11:25:52:625 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:25:52:626 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:25:52:626 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:25:52:626 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:25:52:626 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"deptCode_LIKE":["csb"]
}
2025-07-19 11:25:52:832 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 207ms -------------
2025-07-19 11:26:06:772 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:26:06:772 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:26:06:772 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:26:06:772 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:26:06:773 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"deptCode_LIKE":[""]
}
2025-07-19 11:26:06:988 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 216ms -------------
2025-07-19 11:26:18:642 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:26:18:643 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:26:18:643 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:26:18:643 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:26:18:644 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":["客服"],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"],
	"deptCode_LIKE":[""]
}
2025-07-19 11:26:18:850 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 208ms -------------
2025-07-19 11:28:05:772 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-19 11:28:05:773 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-19 11:28:05:774 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:28:05:774 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-19 11:28:05:962 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 191ms -------------
2025-07-19 11:28:06:096 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-07-19 11:28:06:096 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-07-19 11:28:06:097 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-19 11:28:06:097 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-07-19 11:28:06:169 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 73ms -------------
2025-07-19 11:28:06:503 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-19 11:28:06:503 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-19 11:28:06:503 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:28:06:503 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-19 11:28:06:681 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 178ms -------------
2025-07-19 11:28:09:712 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:28:09:712 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:28:09:712 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:28:09:712 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:28:09:712 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752895689575.394"],
	"typeCode":["common_status"]
}
2025-07-19 11:28:09:932 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 220ms -------------
2025-07-19 11:28:10:102 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:28:10:102 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:28:10:102 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:28:10:102 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:28:10:103 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752895689971.0725"],
	"typeCode":["common_status"]
}
2025-07-19 11:28:10:288 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 186ms -------------
2025-07-19 11:28:10:428 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-19 11:28:10:428 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-19 11:28:10:429 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:28:10:429 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-19 11:28:10:430 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:28:10:430 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:28:10:430 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:28:10:430 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:28:10:430 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:28:10:600 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 172ms -------------
2025-07-19 11:28:10:619 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 189ms -------------
2025-07-19 11:28:49:921 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/insert" 请求开始 -------------
2025-07-19 11:28:49:921 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/insert
2025-07-19 11:28:49:921 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-19 11:28:49:921 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.insert
2025-07-19 11:28:50:393 INFO  [http-nio-7001-exec-3] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=ed2a2ba6-b8e8-4240-8615-e2ff07ed3260, createTime=1752895730390, level=0, moduleId=00, method=org.opsli.modulars.system.department.web.DepartmentRestController.insert, args=[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"name":"测试1部","deptCode":"csb1","parentDepartmentId":1946396151407902722,"status":1,"responsibilities":"","version":0,"izApi":false,"izManual":false}], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 新增部门数据, operationType=insert, runTime=410, returnValue={"msg":"新增部门数据成功","code":0,"timestamp":1752895730387}, tenantId=1944596258485932033, logType=2)
2025-07-19 11:28:50:399 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/insert" 请求结束 => 耗时: 479ms -------------
2025-07-19 11:28:50:532 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:28:50:532 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:28:50:532 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:28:50:532 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:28:50:532 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:28:50:662 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 130ms -------------
2025-07-19 11:30:44:587 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/update" 请求开始 -------------
2025-07-19 11:30:44:589 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/update
2025-07-19 11:30:44:589 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-19 11:30:44:589 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.update
2025-07-19 11:30:44:856 INFO  [http-nio-7001-exec-1] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=e52d2e0d-bc11-4a22-9abd-e14b288d6857, createTime=1752895844856, level=0, moduleId=00, method=org.opsli.modulars.system.department.web.DepartmentRestController.update, args=[{"tenantId":1944596258485932000,"dataMonth":1751299200000,"name":"测试1部","deptCode":"csb2","parentDepartmentId":1946396151407902700,"status":1,"responsibilities":"","id":"1946411883277549570","createBy":"1944596700989198337","createTime":1752895730000,"version":0,"izApi":false,"izManual":false}], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 修改部门数据, operationType=update, runTime=253, returnValue={"msg":"修改部门数据成功","code":0,"timestamp":1752895844855}, tenantId=1944596258485932033, logType=2)
2025-07-19 11:30:44:858 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/update" 请求结束 => 耗时: 271ms -------------
2025-07-19 11:30:45:001 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:30:45:002 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:30:45:003 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:30:45:003 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:30:45:004 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:30:45:121 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 121ms -------------
2025-07-19 11:30:52:991 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:30:52:991 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:30:52:991 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:30:52:992 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:30:52:992 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["2"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:30:53:117 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 126ms -------------
2025-07-19 11:30:55:260 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:30:55:261 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:30:55:261 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:30:55:261 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:30:55:261 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:30:55:384 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 124ms -------------
2025-07-19 11:34:25:480 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/update" 请求开始 -------------
2025-07-19 11:34:25:481 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/update
2025-07-19 11:34:25:482 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-19 11:34:25:482 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.update
2025-07-19 11:34:25:728 INFO  [http-nio-7001-exec-10] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=0c04ef16-7642-4cdb-b074-5aaec0a088ba, createTime=1752896065728, level=0, moduleId=00, method=org.opsli.modulars.system.department.web.DepartmentRestController.update, args=[{"tenantId":1944596258485932000,"dataMonth":1751299200000,"name":"测试部啊","deptCode":"csb","status":1,"responsibilities":"","id":"1946396151407902722","createBy":"1944596700989198337","createTime":1752891979000,"version":0,"izApi":false,"izManual":false}], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 修改部门数据, operationType=update, runTime=234, returnValue={"msg":"修改部门数据成功","code":0,"timestamp":1752896065727}, tenantId=1944596258485932033, logType=2)
2025-07-19 11:34:25:731 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/update" 请求结束 => 耗时: 251ms -------------
2025-07-19 11:34:25:867 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:34:25:867 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:34:25:867 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:34:25:867 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:34:25:867 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:34:25:984 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 117ms -------------
2025-07-19 11:34:46:640 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/insert" 请求开始 -------------
2025-07-19 11:34:46:641 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/insert
2025-07-19 11:34:46:641 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-19 11:34:46:641 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.insert
2025-07-19 11:34:46:829 INFO  [http-nio-7001-exec-6] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=ad7ca5fc-0341-44fc-bf5e-bb75f58f5d7f, createTime=1752896086829, level=0, moduleId=00, method=org.opsli.modulars.system.department.web.DepartmentRestController.insert, args=[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"name":"测试部","deptCode":"cs","status":1,"responsibilities":"","version":0,"izApi":false,"izManual":false}], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 新增部门数据, operationType=insert, runTime=183, returnValue={"msg":"新增部门数据成功","code":0,"timestamp":1752896086828}, tenantId=1944596258485932033, logType=2)
2025-07-19 11:34:46:832 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/insert" 请求结束 => 耗时: 191ms -------------
2025-07-19 11:34:46:969 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:34:46:969 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:34:46:969 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:34:46:969 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:34:46:969 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:34:47:091 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 122ms -------------
2025-07-19 11:35:03:885 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:35:03:885 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:35:03:885 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:35:03:885 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:35:03:885 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["2"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:35:03:995 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 110ms -------------
2025-07-19 11:35:06:217 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:35:06:217 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:35:06:217 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:35:06:217 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:35:06:217 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:35:06:343 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 126ms -------------
2025-07-19 11:39:16:721 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 23.0.1 with PID 73722 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-07-19 11:39:16:721 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
2025-07-19 11:39:17:938 INFO  [main] org.apache.catalina.core.StandardService-? Starting service [Tomcat]
2025-07-19 11:39:17:938 INFO  [main] org.apache.catalina.core.StandardEngine-? Starting Servlet engine: [Apache Tomcat/10.1.41]
2025-07-19 11:39:17:968 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring embedded WebApplicationContext
2025-07-19 11:39:19:058 INFO  [main] com.alibaba.druid.pool.DruidDataSource-? {dataSource-1,master} inited
2025-07-19 11:39:19:060 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource - add a datasource named [master] success
2025-07-19 11:39:19:060 INFO  [main] c.b.dynamic.datasource.DynamicRoutingDataSource-? dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-19 11:39:19:623 WARN  [main] i.n.resolver.dns.DnsServerAddressStreamProviders-? Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-07-19 11:39:20:704 INFO  [main] o.o.p.redisson.strategy.impl.StandaloneConfigImpl-? 初始化[单机部署]方式Config,redisAddress:*************:6379
2025-07-19 11:39:20:729 INFO  [main] org.redisson.Version-? Redisson 3.48.0
2025-07-19 11:39:20:837 INFO  [redisson-netty-5-8] org.redisson.connection.ConnectionsHolder-? 1 connections initialized for *************/*************:6379
2025-07-19 11:39:21:584 INFO  [redisson-netty-5-19] org.redisson.connection.ConnectionsHolder-? 24 connections initialized for *************/*************:6379
2025-07-19 11:39:21:618 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonManager]组装完毕,当前连接方式:单节点部署方式,连接地址:*************:6379
2025-07-19 11:39:21:619 INFO  [main] org.opsli.plugins.redisson.conf.RedissonConfig-? [RedissonLock]组装完毕
2025-07-19 11:39:23:111 INFO  [main] org.opsli.OpsliApplication-? Started OpsliApplication in 6.658 seconds (process running for 6.906)
2025-07-19 11:39:32:023 INFO  [http-nio-7001-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/opsli-boot]-? Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 11:39:32:349 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-19 11:39:32:349 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-19 11:39:32:349 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:39:32:349 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-19 11:39:32:518 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 169ms -------------
2025-07-19 11:39:32:611 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-07-19 11:39:32:611 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-07-19 11:39:32:611 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-19 11:39:32:611 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-07-19 11:39:32:666 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 55ms -------------
2025-07-19 11:39:32:908 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:39:32:909 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:39:32:909 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:39:32:909 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:39:32:910 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752896372821.1074"],
	"typeCode":["common_status"]
}
2025-07-19 11:39:33:213 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 305ms -------------
2025-07-19 11:39:33:328 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:39:33:328 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:39:33:328 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:39:33:328 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:39:33:329 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752896373241.5532"],
	"typeCode":["common_status"]
}
2025-07-19 11:39:33:449 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 121ms -------------
2025-07-19 11:39:33:551 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-19 11:39:33:552 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-19 11:39:33:552 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:39:33:552 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-19 11:39:33:556 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:39:33:556 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:39:33:556 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:39:33:556 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:39:33:557 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:39:33:676 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 125ms -------------
2025-07-19 11:39:33:754 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 198ms -------------
2025-07-19 11:39:59:425 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-19 11:39:59:428 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-19 11:39:59:428 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:39:59:428 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-19 11:39:59:563 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 138ms -------------
2025-07-19 11:39:59:659 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-07-19 11:39:59:660 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-07-19 11:39:59:660 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-19 11:39:59:660 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-07-19 11:39:59:713 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 54ms -------------
2025-07-19 11:39:59:960 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:39:59:960 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:39:59:960 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:39:59:960 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:39:59:960 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752896399873.1428"],
	"typeCode":["common_status"]
}
2025-07-19 11:40:00:089 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 129ms -------------
2025-07-19 11:40:00:206 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:40:00:206 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:40:00:206 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:40:00:206 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:40:00:207 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752896400105.717"],
	"typeCode":["common_status"]
}
2025-07-19 11:40:00:332 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 126ms -------------
2025-07-19 11:40:00:443 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-19 11:40:00:443 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-19 11:40:00:444 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:40:00:444 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-19 11:40:00:460 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:40:00:460 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:40:00:460 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:40:00:460 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:40:00:461 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:40:00:570 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 127ms -------------
2025-07-19 11:40:00:598 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 138ms -------------
2025-07-19 11:40:18:295 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/insert" 请求开始 -------------
2025-07-19 11:40:18:295 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/insert
2025-07-19 11:40:18:295 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-19 11:40:18:295 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.insert
2025-07-19 11:40:18:493 INFO  [http-nio-7001-exec-1] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=9bc7a447-8298-4297-838c-bde730a0751e, createTime=1752896418488, level=0, moduleId=00, method=org.opsli.modulars.system.department.web.DepartmentRestController.insert, args=[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"name":"测试2部","deptCode":"csb2","status":1,"responsibilities":"","version":0,"izApi":false,"izManual":false}], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 新增部门数据, operationType=insert, runTime=109, returnValue=null, tenantId=1944596258485932033, logType=2)
2025-07-19 11:40:18:500 WARN  [http-nio-7001-exec-1] org.opsli.core.handler.GlobalExceptionHandler-? 业务异常 - 异常编号：20901 - 异常信息：部门编码已存在
2025-07-19 11:40:18:504 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/insert" 请求结束 => 耗时: 209ms -------------
2025-07-19 11:40:25:731 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/insert" 请求开始 -------------
2025-07-19 11:40:25:731 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/insert
2025-07-19 11:40:25:731 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-19 11:40:25:731 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.insert
2025-07-19 11:40:25:911 INFO  [http-nio-7001-exec-4] org.opsli.core.log.aspect.OperateLogAspect-? 记录日志:OperationLog(id=1efd896c-e043-4e52-8dce-1943342c9546, createTime=1752896425911, level=0, moduleId=00, method=org.opsli.modulars.system.department.web.DepartmentRestController.insert, args=[{"tenantId":1944596258485932033,"dataMonth":1751299200000,"name":"测试2部","deptCode":"csb3","status":1,"responsibilities":"","version":0,"izApi":false,"izManual":false}], userId=1944596700989198337, username=admin_cyj, realName=admin, description='admin'=> 新增部门数据, operationType=insert, runTime=178, returnValue={"msg":"新增部门数据成功","code":0,"timestamp":1752896425911}, tenantId=1944596258485932033, logType=2)
2025-07-19 11:40:25:913 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/insert" 请求结束 => 耗时: 182ms -------------
2025-07-19 11:40:26:014 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:40:26:014 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:40:26:014 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:40:26:014 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:40:26:014 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:40:26:138 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 124ms -------------
2025-07-19 11:41:21:049 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:41:21:050 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:41:21:050 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:41:21:050 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:41:21:050 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["20"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:41:21:199 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 150ms -------------
2025-07-19 11:41:29:795 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:41:29:795 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:41:29:795 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:41:29:795 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:41:29:796 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:41:29:923 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 128ms -------------
2025-07-19 11:41:41:018 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:41:41:018 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:41:41:018 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:41:41:018 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:41:41:018 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["20"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:41:41:144 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 126ms -------------
2025-07-19 11:41:44:553 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:41:44:553 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:41:44:553 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:41:44:553 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:41:44:553 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:41:44:682 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 129ms -------------
2025-07-19 11:41:47:773 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-19 11:41:47:773 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-19 11:41:47:773 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:41:47:773 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-19 11:41:47:901 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 128ms -------------
2025-07-19 11:41:47:997 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-07-19 11:41:47:998 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-07-19 11:41:47:998 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-19 11:41:47:998 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-07-19 11:41:48:046 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 49ms -------------
2025-07-19 11:41:48:291 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:41:48:291 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:41:48:291 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:41:48:291 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:41:48:291 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752896508202.3984"],
	"typeCode":["common_status"]
}
2025-07-19 11:41:48:411 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 120ms -------------
2025-07-19 11:41:48:523 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:41:48:524 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:41:48:524 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:41:48:524 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:41:48:524 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752896508433.1758"],
	"typeCode":["common_status"]
}
2025-07-19 11:41:48:641 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 118ms -------------
2025-07-19 11:41:48:748 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-19 11:41:48:748 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:41:48:749 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-19 11:41:48:749 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:41:48:749 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:41:48:749 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:41:48:749 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:41:48:749 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-19 11:41:48:749 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:41:48:872 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 124ms -------------
2025-07-19 11:41:48:874 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 126ms -------------
2025-07-19 11:42:06:941 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:42:06:941 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:42:06:941 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:42:06:941 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:42:06:941 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["2"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:42:07:068 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 128ms -------------
2025-07-19 11:42:08:937 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:42:08:937 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:42:08:937 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:42:08:937 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:42:08:937 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:42:09:061 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 124ms -------------
2025-07-19 11:42:11:386 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:42:11:386 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:42:11:386 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:42:11:386 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:42:11:387 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["2"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:42:11:513 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 127ms -------------
2025-07-19 11:42:12:824 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:42:12:824 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:42:12:824 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:42:12:824 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:42:12:825 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:42:12:950 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 126ms -------------
2025-07-19 11:43:06:817 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-19 11:43:06:821 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-19 11:43:06:821 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:43:06:821 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-19 11:43:06:954 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 139ms -------------
2025-07-19 11:43:16:572 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:43:16:572 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:43:16:572 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:43:16:572 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:43:16:573 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752896596473.8857"],
	"typeCode":["common_status"]
}
2025-07-19 11:43:16:731 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 159ms -------------
2025-07-19 11:43:16:853 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:43:16:854 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:43:16:854 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:43:16:854 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:43:16:854 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752896596762.099"],
	"typeCode":["common_status"]
}
2025-07-19 11:43:16:970 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 117ms -------------
2025-07-19 11:43:17:080 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-19 11:43:17:080 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:43:17:080 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-19 11:43:17:080 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:43:17:080 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:43:17:080 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:43:17:080 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:43:17:080 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-19 11:43:17:081 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:43:17:204 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 124ms -------------
2025-07-19 11:43:17:214 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 134ms -------------
2025-07-19 11:44:59:578 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-19 11:44:59:579 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-19 11:44:59:579 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:44:59:579 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-19 11:44:59:711 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 133ms -------------
2025-07-19 11:44:59:815 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-07-19 11:44:59:815 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-07-19 11:44:59:815 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-19 11:44:59:815 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-07-19 11:44:59:861 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 46ms -------------
2025-07-19 11:45:00:250 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:45:00:251 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:45:00:251 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:45:00:251 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:45:00:251 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752896700156.3755"],
	"typeCode":["common_status"]
}
2025-07-19 11:45:00:387 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 137ms -------------
2025-07-19 11:45:00:507 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:45:00:507 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:45:00:507 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:45:00:507 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:45:00:507 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752896700411.868"],
	"typeCode":["common_status"]
}
2025-07-19 11:45:00:625 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 117ms -------------
2025-07-19 11:45:00:762 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-19 11:45:00:762 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-19 11:45:00:763 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:45:00:763 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-19 11:45:00:789 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:45:00:790 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:45:00:790 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:45:00:790 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:45:00:791 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:45:00:964 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 202ms -------------
2025-07-19 11:45:01:056 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 267ms -------------
2025-07-19 11:46:14:541 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:46:14:542 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:46:14:542 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:46:14:542 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:46:14:544 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752896774387.035"],
	"typeCode":["common_status"]
}
2025-07-19 11:46:14:691 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 150ms -------------
2025-07-19 11:46:14:805 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:46:14:805 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:46:14:805 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:46:14:805 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:46:14:805 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752896774719.5232"],
	"typeCode":["common_status"]
}
2025-07-19 11:46:14:922 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 117ms -------------
2025-07-19 11:46:15:025 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:46:15:025 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:46:15:025 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:46:15:025 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:46:15:025 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:46:15:287 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 262ms -------------
2025-07-19 11:53:42:759 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/logs/findPage" 请求开始 -------------
2025-07-19 11:53:42:760 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/logs/findPage
2025-07-19 11:53:42:760 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:53:42:760 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LogsRestController.findPage
2025-07-19 11:53:42:760 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"type_EQ":[""],
	"createTime_BEGIN":[""],
	"createTime_END":[""]
}
2025-07-19 11:53:42:777 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/logs/findPage" 请求结束 => 耗时: 18ms -------------
2025-07-19 11:53:44:406 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:53:44:406 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:53:44:406 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:53:44:406 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:53:44:407 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752897224304.2087"],
	"typeCode":["common_status"]
}
2025-07-19 11:53:44:546 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 140ms -------------
2025-07-19 11:53:44:664 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:53:44:664 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:53:44:664 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:53:44:664 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:53:44:664 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752897224567.6602"],
	"typeCode":["common_status"]
}
2025-07-19 11:53:44:781 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 117ms -------------
2025-07-19 11:53:44:894 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:53:44:894 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:53:44:894 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:53:44:894 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:53:44:894 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:53:45:018 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 124ms -------------
2025-07-19 11:59:15:115 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:59:15:116 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:59:15:116 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:59:15:116 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:59:15:116 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752897555005.6047"],
	"typeCode":["common_status"]
}
2025-07-19 11:59:15:253 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 138ms -------------
2025-07-19 11:59:15:363 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:59:15:363 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:59:15:363 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:59:15:363 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:59:15:363 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752897555269.4275"],
	"typeCode":["common_status"]
}
2025-07-19 11:59:15:474 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 111ms -------------
2025-07-19 11:59:15:581 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:59:15:581 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:59:15:581 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:59:15:581 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:59:15:581 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:59:15:704 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 123ms -------------
2025-07-19 11:59:47:201 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:59:47:202 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:59:47:202 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:59:47:202 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:59:47:202 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752897587094.4893"],
	"typeCode":["common_status"]
}
2025-07-19 11:59:47:328 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 127ms -------------
2025-07-19 11:59:47:445 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 11:59:47:446 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 11:59:47:446 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:59:47:446 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 11:59:47:446 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752897587356.5125"],
	"typeCode":["common_status"]
}
2025-07-19 11:59:47:556 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 111ms -------------
2025-07-19 11:59:47:686 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 11:59:47:686 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 11:59:47:686 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:59:47:686 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 11:59:47:686 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:59:47:702 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/listAll" 请求开始 -------------
2025-07-19 11:59:47:702 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/listAll
2025-07-19 11:59:47:702 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 11:59:47:702 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 11:59:47:714 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/listAll" 请求结束 => 耗时: 12ms -------------
2025-07-19 11:59:47:822 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 136ms -------------
2025-07-19 12:03:32:668 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求开始 -------------
2025-07-19 12:03:32:670 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/common/public-key
2025-07-19 12:03:32:670 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 12:03:32:670 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: CommonRestController.getPublicKey
2025-07-19 12:03:32:672 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752897812643.295"]
}
2025-07-19 12:03:32:718 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求结束 => 耗时: 50ms -------------
2025-07-19 12:03:32:741 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-07-19 12:03:32:741 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-07-19 12:03:32:741 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-19 12:03:32:741 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LoginByAccountRestController.login
2025-07-19 12:03:33:326 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 585ms -------------
2025-07-19 12:03:33:433 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-19 12:03:33:433 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-19 12:03:33:433 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 12:03:33:433 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-19 12:03:33:559 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 126ms -------------
2025-07-19 12:03:33:650 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-07-19 12:03:33:650 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-07-19 12:03:33:650 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-19 12:03:33:650 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-07-19 12:03:33:693 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 43ms -------------
2025-07-19 12:03:33:967 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-19 12:03:33:967 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-19 12:03:33:967 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 12:03:33:967 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-19 12:03:34:092 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 125ms -------------
2025-07-19 12:03:37:820 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 12:03:37:820 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 12:03:37:820 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 12:03:37:820 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 12:03:37:820 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752897817714.054"],
	"typeCode":["common_status"]
}
2025-07-19 12:03:37:935 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 115ms -------------
2025-07-19 12:03:38:049 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 12:03:38:049 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 12:03:38:049 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 12:03:38:049 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 12:03:38:049 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752897817959.5083"],
	"typeCode":["common_status"]
}
2025-07-19 12:03:38:165 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 116ms -------------
2025-07-19 12:03:38:271 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-19 12:03:38:272 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-19 12:03:38:272 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 12:03:38:272 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-19 12:03:38:277 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 12:03:38:277 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 12:03:38:277 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 12:03:38:277 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 12:03:38:277 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/listAll" 请求开始 -------------
2025-07-19 12:03:38:277 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/listAll
2025-07-19 12:03:38:277 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 12:03:38:277 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 12:03:38:277 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 12:03:38:286 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/listAll" 请求结束 => 耗时: 9ms -------------
2025-07-19 12:03:38:395 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 124ms -------------
2025-07-19 12:03:38:416 INFO  [http-nio-7001-exec-6] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 139ms -------------
2025-07-19 12:06:08:425 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-07-19 12:06:08:426 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-07-19 12:06:08:427 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-19 12:06:08:427 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LoginByAccountRestController.login
2025-07-19 12:06:08:930 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 505ms -------------
2025-07-19 12:06:09:038 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-19 12:06:09:038 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-19 12:06:09:038 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 12:06:09:038 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-19 12:06:09:162 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 124ms -------------
2025-07-19 12:06:09:255 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-07-19 12:06:09:255 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-07-19 12:06:09:255 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-19 12:06:09:255 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-07-19 12:06:09:301 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 46ms -------------
2025-07-19 12:06:09:693 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 12:06:09:693 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 12:06:09:693 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 12:06:09:693 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 12:06:09:694 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752897969606.1165"],
	"typeCode":["common_status"]
}
2025-07-19 12:06:09:808 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 115ms -------------
2025-07-19 12:06:09:928 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 12:06:09:928 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 12:06:09:928 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 12:06:09:928 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 12:06:09:928 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752897969837.9473"],
	"typeCode":["common_status"]
}
2025-07-19 12:06:10:047 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 119ms -------------
2025-07-19 12:06:10:148 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-19 12:06:10:148 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-19 12:06:10:148 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 12:06:10:148 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-19 12:06:10:159 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 12:06:10:159 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/listAll" 请求开始 -------------
2025-07-19 12:06:10:159 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 12:06:10:159 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/listAll
2025-07-19 12:06:10:159 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 12:06:10:159 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 12:06:10:159 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 12:06:10:159 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 12:06:10:159 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 12:06:10:164 INFO  [http-nio-7001-exec-7] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/listAll" 请求结束 => 耗时: 5ms -------------
2025-07-19 12:06:10:281 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 133ms -------------
2025-07-19 12:06:10:308 INFO  [http-nio-7001-exec-3] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 149ms -------------
2025-07-19 12:09:27:095 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求开始 -------------
2025-07-19 12:09:27:097 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/common/public-key
2025-07-19 12:09:27:097 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 12:09:27:097 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: CommonRestController.getPublicKey
2025-07-19 12:09:27:097 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752898167082.5403"]
}
2025-07-19 12:09:27:120 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/common/public-key" 请求结束 => 耗时: 25ms -------------
2025-07-19 12:09:27:148 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求开始 -------------
2025-07-19 12:09:27:148 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/system/login
2025-07-19 12:09:27:148 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-19 12:09:27:148 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: LoginByAccountRestController.login
2025-07-19 12:09:27:693 INFO  [http-nio-7001-exec-9] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/system/login" 请求结束 => 耗时: 546ms -------------
2025-07-19 12:09:27:801 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-19 12:09:27:801 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-19 12:09:27:801 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 12:09:27:801 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-19 12:09:27:929 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 128ms -------------
2025-07-19 12:09:28:020 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求开始 -------------
2025-07-19 12:09:28:021 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/menu/findMenuTree
2025-07-19 12:09:28:021 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: POST
2025-07-19 12:09:28:021 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: MenuRestController.findMenuTree
2025-07-19 12:09:28:066 INFO  [http-nio-7001-exec-2] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/menu/findMenuTree" 请求结束 => 耗时: 46ms -------------
2025-07-19 12:09:28:477 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 12:09:28:477 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 12:09:28:477 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 12:09:28:477 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 12:09:28:478 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752898168392.437"],
	"typeCode":["common_status"]
}
2025-07-19 12:09:28:590 INFO  [http-nio-7001-exec-8] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 113ms -------------
2025-07-19 12:09:28:703 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求开始 -------------
2025-07-19 12:09:28:703 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/dict/getDictListByCode
2025-07-19 12:09:28:703 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 12:09:28:703 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DictRestController.getDictListByCode
2025-07-19 12:09:28:704 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"t":["1752898168615.0303"],
	"typeCode":["common_status"]
}
2025-07-19 12:09:28:818 INFO  [http-nio-7001-exec-5] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/dict/getDictListByCode" 请求结束 => 耗时: 115ms -------------
2025-07-19 12:09:28:926 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求开始 -------------
2025-07-19 12:09:28:926 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/user/getInfo
2025-07-19 12:09:28:926 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 12:09:28:926 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: UserRestController.getInfo
2025-07-19 12:09:28:934 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求开始 -------------
2025-07-19 12:09:28:935 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/findPage
2025-07-19 12:09:28:935 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 12:09:28:935 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/listAll" 请求开始 -------------
2025-07-19 12:09:28:935 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方法: DepartmentRestController.findPage
2025-07-19 12:09:28:935 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求URL: http://127.0.0.1:7001/opsli-boot/api/v1/system/department/listAll
2025-07-19 12:09:28:935 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求方式: GET
2025-07-19 12:09:28:935 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 12:09:28:935 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? 请求参数(Body): 
{
	"pageNo":["1"],
	"pageSize":["10"],
	"name_LIKE":[""],
	"tenantId":["1944596258485932033"],
	"dataMonth":["2025-07"]
}
2025-07-19 12:09:28:940 INFO  [http-nio-7001-exec-4] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/listAll" 请求结束 => 耗时: 5ms -------------
2025-07-19 12:09:29:083 INFO  [http-nio-7001-exec-1] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/user/getInfo" 请求结束 => 耗时: 157ms -------------
2025-07-19 12:09:29:096 INFO  [http-nio-7001-exec-10] o.o.core.general.interceptor.RequestLogInterceptor-? ------------- "/opsli-boot/api/v1/system/department/findPage" 请求结束 => 耗时: 162ms -------------
2025-07-19 12:09:42:511 INFO  [main] org.opsli.OpsliApplication-? Starting OpsliApplication using Java 23.0.1 with PID 76536 (/Users/<USER>/workspace/github/opsli/opsli-boot/opsli-starter/target/classes started by xin in /Users/<USER>/workspace/github/opsli)
2025-07-19 12:09:42:512 INFO  [main] org.opsli.OpsliApplication-? The following 1 profile is active: "dev"
